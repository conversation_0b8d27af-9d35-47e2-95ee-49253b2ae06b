{"metadata": {"version": "3.1.0", "last_updated": "2025-01-06T22:06:00.000Z"}, "workflow_state": {"current_step": "P02-S03-T01-Market-Size-and-Trends-Analysis", "current_phase": "phase_2", "current_task": "P02-S03-T01-Market-Size-and-Trends-Analysis", "previous_step": "P02-S02-T07-Validation-Synthesis-Recommendation", "next_step": "P02-S03-T02-Competitive-Intelligence-Deep-Dive", "progress": {"total_steps": 23, "completed_steps": 7, "current_step_number": 8, "percentage": 30, "phase_1_completed": true, "phase_2_completed": false, "phase_3_started": false}, "session": {"started_at": "2025-01-27T10:00:00.000Z", "last_updated": "2025-01-27T14:15:00.000Z", "status": "phase_2_market_research", "project_type": "transport_ecosystem"}}, "agents": {"uber-orchestrator-agent": {"id": "ORCH-001", "name": "Uber Orchestrator Agent", "capabilities": ["workflow_coordination", "state_management", "task_delegation", "progress_tracking"], "persona": "system_orchestrator", "phases": ["all"], "workflow_steps": ["all"]}, "nlu-processor-agent": {"id": "NLU-001", "name": "NLU Processor Agent", "capabilities": ["user_profiling", "requirement_extraction", "natural_language_processing"], "persona": "expert_analyst", "phases": ["phase_1"], "workflow_steps": ["P01-S01-T01-User-Profile-Development"]}, "elicitation-agent": {"id": "ELICIT-001", "name": "Requirements Elicitation Agent", "capabilities": ["requirement_extraction", "user_interview", "stakeholder_mapping", "requirements_analysis"], "persona": "expert_business_analyst", "phases": ["phase_1", "phase_2"], "workflow_steps": ["P01-S01-T02-Project-Vision-Elicitation", "P01-S01-T03-Success-Criteria-Definition", "P01-S01-T04-Requirement-Analysis", "P01-S01-T05-Technical-Constraints", "P02-S02-T01-Problem-Statement-Refinement"]}, "market-research-agent": {"id": "MARKET-001", "name": "Market Research Agent", "capabilities": ["market_analysis", "competitive_intelligence", "customer_research", "trend_analysis", "business_strategy"], "persona": "expert_market_analyst", "phases": ["phase_2"], "workflow_steps": ["P02-S02-T02-Market-Opportunity-Analysis", "P02-S02-T03-User-Validation-Research", "P02-S02-T04-Competitive-Landscape-Analysis", "P02-S02-T06-Business-Viability-Analysis", "P02-S02-T07-Validation-Synthesis-Recommendation", "P02-S03-T01-Market-Size-and-Trends-Analysis", "P02-S03-T02-Competitive-Intelligence-Deep-Dive", "P02-S03-T03-Customer-Segmentation-and-Persona-Development", "P02-S03-T04-Value-Chain-Analysis", "P02-S03-T06-Strategic-Opportunity-Identification", "P02-S03-T07-Market-Entry-Strategy-Development", "P02-S03-T08-Research-Synthesis-and-Strategic-Recommendations", "P02-S04-T01-Business-Model-Design", "P02-S04-T02-Revenue-Strategy-Development", "P02-S04-T04-Strategic-Partnership-Framework", "P02-S04-T05-Risk-Assessment-and-Mitigation", "P02-S04-T06-Financial-Planning-and-Projections", "P02-S04-T07-Implementation-Strategy-and-Roadmap", "P02-S04-T08-Strategy-Synthesis-and-Implementation-Planning"]}, "technology-advisor-agent": {"id": "TECH-001", "name": "Technology Advisor Agent", "capabilities": ["technology_assessment", "feasibility_analysis", "stack_recommendation", "trend_analysis"], "persona": "expert_technology_consultant", "phases": ["phase_2"], "workflow_steps": ["P02-S02-T05-Technical-Feasibility-Assessment", "P02-S03-T05-Technology-Trend-Analysis"]}, "marketing-strategy-orchestrator": {"id": "MARKETING-001", "name": "Marketing Strategy Orchestrator", "capabilities": ["go_to_market_strategy", "marketing_planning", "channel_strategy", "brand_positioning"], "persona": "expert_marketing_strategist", "phases": ["phase_2"], "workflow_steps": ["P02-S04-T03-Go-to-Market-Strategy"]}}, "workflow_progression": {"step_sequence": ["P01-S01-T01-User-Profile-Development", "P01-S01-T02-Project-Vision-Elicitation", "P01-S01-T03-Success-Criteria-Definition", "P01-S01-T04-Requirement-Analysis", "P01-S01-T05-Technical-Constraints", "P02-S02-T01-Problem-Statement-Refinement", "P02-S02-T02-Market-Opportunity-Analysis", "P02-S02-T03-User-Validation-Research", "P02-S02-T04-Competitive-Landscape-Analysis", "P02-S02-T05-Technical-Feasibility-Assessment", "P02-S02-T06-Business-Viability-Analysis", "P02-S02-T07-Validation-Synthesis-Recommendation", "P02-S03-T01-Market-Size-and-Trends-Analysis", "P02-S03-T02-Competitive-Intelligence-Deep-Dive", "P02-S03-T03-Customer-Segmentation-and-Persona-Development", "P02-S03-T04-Value-Chain-Analysis", "P02-S03-T05-Technology-Trend-Analysis", "P02-S03-T06-Strategic-Opportunity-Identification", "P02-S03-T07-Market-Entry-Strategy-Development", "P02-S03-T08-Research-Synthesis-and-Strategic-Recommendations", "P02-S04-T01-Business-Model-Design", "P02-S04-T02-Revenue-Strategy-Development", "P02-S04-T03-Go-to-Market-Strategy", "P02-S04-T04-Strategic-Partnership-Framework", "P02-S04-T05-Risk-Assessment-and-Mitigation", "P02-S04-T06-Financial-Planning-and-Projections", "P02-S04-T07-Implementation-Strategy-and-Roadmap", "P02-S04-T08-Strategy-Synthesis-and-Implementation-Planning"], "auto_progression": true, "require_completion_confirmation": false}, "step_definitions": {"P01-S01-T01-User-Profile-Development": {"agent": "nlu-processor-agent", "phase": "phase_1", "task_id": "P01-S01-T01", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T01-User-Profile-Development.md", "estimated_duration_minutes": 30, "previous_task": null, "next_task": "P01-S01-T02-Project-Vision-Elicitation"}, "P01-S01-T02-Project-Vision-Elicitation": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T02", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T02-Project-Vision-Elicitation.md", "estimated_duration_minutes": 45, "previous_task": "P01-S01-T01-User-Profile-Development", "next_task": "P01-S01-T03-Success-Criteria-Definition"}, "P01-S01-T03-Success-Criteria-Definition": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T03", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T03-Success-Criteria-Definition.md", "estimated_duration_minutes": 30, "previous_task": "P01-S01-T02-Project-Vision-Elicitation", "next_task": "P01-S01-T04-Requirement-Analysis"}, "P01-S01-T04-Requirement-Analysis": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T04", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T04-Requirement-Analysis.md", "estimated_duration_minutes": 60, "previous_task": "P01-S01-T03-Success-Criteria-Definition", "next_task": "P01-S01-T05-Technical-Constraints"}, "P01-S01-T05-Technical-Constraints": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T05", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T05-Technical-Constraints.md", "estimated_duration_minutes": 30, "previous_task": "P01-S01-T04-Requirement-Analysis", "next_task": "P02-S02-T01-Problem-Statement-Refinement"}, "P02-S02-T01-Problem-Statement-Refinement": {"agent": "elicitation-agent", "phase": "phase_2", "task_id": "P02-S02-T01", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/02_Problem_Validation/P02-S02-T01-Problem-Statement-Refinement.md", "estimated_duration_minutes": 45, "previous_task": "P01-S01-T05-Technical-Constraints", "next_task": "P02-S02-T02-Market-Opportunity-Analysis"}, "P02-S02-T02-Market-Opportunity-Analysis": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S02-T02", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/02_Problem_Validation/P02-S02-T02-Market-Opportunity-Analysis.md", "estimated_duration_minutes": 60, "previous_task": "P02-S02-T01-Problem-Statement-Refinement", "next_task": "P02-S02-T03-User-Validation-Research"}, "P02-S02-T03-User-Validation-Research": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S02-T03", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/02_Problem_Validation/P02-S02-T03-User-Validation-Research.md", "estimated_duration_minutes": 90, "previous_task": "P02-S02-T02-Market-Opportunity-Analysis", "next_task": "P02-S02-T04-Competitive-Landscape-Analysis"}, "P02-S02-T04-Competitive-Landscape-Analysis": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S02-T04", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/02_Problem_Validation/P02-S02-T04-Competitive-Landscape-Analysis.md", "estimated_duration_minutes": 75, "previous_task": "P02-S02-T03-User-Validation-Research", "next_task": "P02-S02-T05-Technical-Feasibility-Assessment"}, "P02-S02-T05-Technical-Feasibility-Assessment": {"agent": "technology-advisor-agent", "phase": "phase_2", "task_id": "P02-S02-T05", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/02_Problem_Validation/P02-S02-T05-Technical-Feasibility-Assessment.md", "estimated_duration_minutes": 120, "previous_task": "P02-S02-T04-Competitive-Landscape-Analysis", "next_task": "P02-S02-T06-Business-Viability-Analysis"}, "P02-S02-T06-Business-Viability-Analysis": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S02-T06", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/02_Problem_Validation/P02-S02-T06-Business-Viability-Analysis.md", "estimated_duration_minutes": 90, "previous_task": "P02-S02-T05-Technical-Feasibility-Assessment", "next_task": "P02-S02-T07-Validation-Synthesis-Recommendation"}, "P02-S02-T07-Validation-Synthesis-Recommendation": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S02-T07", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/02_Problem_Validation/P02-S02-T07-Validation-Synthesis-Recommendation.md", "estimated_duration_minutes": 60, "previous_task": "P02-S02-T06-Business-Viability-Analysis", "next_task": "P02-S03-T01-Market-Size-and-Trends-Analysis"}, "P02-S03-T01-Market-Size-and-Trends-Analysis": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S03-T01", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T01-Market-Size-and-Trends-Analysis.md", "estimated_duration_minutes": 90, "previous_task": "P02-S02-T07-Validation-Synthesis-Recommendation", "next_task": "P02-S03-T02-Competitive-Intelligence-Deep-Dive"}, "P02-S03-T02-Competitive-Intelligence-Deep-Dive": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S03-T02", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T02-Competitive-Intelligence-Deep-Dive.md", "estimated_duration_minutes": 120, "previous_task": "P02-S03-T01-Market-Size-and-Trends-Analysis", "next_task": "P02-S03-T03-Customer-Segmentation-and-Persona-Development"}, "P02-S03-T03-Customer-Segmentation-and-Persona-Development": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S03-T03", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T03-Customer-Segmentation-and-Persona-Development.md", "estimated_duration_minutes": 105, "previous_task": "P02-S03-T02-Competitive-Intelligence-Deep-Dive", "next_task": "P02-S03-T04-Value-Chain-Analysis"}, "P02-S03-T04-Value-Chain-Analysis": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S03-T04", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T04-Value-Chain-Analysis.md", "estimated_duration_minutes": 90, "previous_task": "P02-S03-T03-Customer-Segmentation-and-Persona-Development", "next_task": "P02-S03-T05-Technology-Trend-Analysis"}, "P02-S03-T05-Technology-Trend-Analysis": {"agent": "technology-advisor-agent", "phase": "phase_2", "task_id": "P02-S03-T05", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T05-Technology-Trend-Analysis.md", "estimated_duration_minutes": 75, "previous_task": "P02-S03-T04-Value-Chain-Analysis", "next_task": "P02-S03-T06-Strategic-Opportunity-Identification"}, "P02-S03-T06-Strategic-Opportunity-Identification": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S03-T06", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T06-Strategic-Opportunity-Identification.md", "estimated_duration_minutes": 90, "previous_task": "P02-S03-T05-Technology-Trend-Analysis", "next_task": "P02-S03-T07-Market-Entry-Strategy-Development"}, "P02-S03-T07-Market-Entry-Strategy-Development": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S03-T07", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T07-Market-Entry-Strategy-Development.md", "estimated_duration_minutes": 105, "previous_task": "P02-S03-T06-Strategic-Opportunity-Identification", "next_task": "P02-S03-T08-Research-Synthesis-and-Strategic-Recommendations"}, "P02-S03-T08-Research-Synthesis-and-Strategic-Recommendations": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S03-T08", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/03_Market_Research/P02-S03-T08-Research-Synthesis-and-Strategic-Recommendations.md", "estimated_duration_minutes": 90, "previous_task": "P02-S03-T07-Market-Entry-Strategy-Development", "next_task": "P02-S04-T01-Business-Model-Design"}, "P02-S04-T01-Business-Model-Design": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S04-T01", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T01-Business-Model-Design.md", "estimated_duration_minutes": 120, "previous_task": "P02-S03-T08-Research-Synthesis-and-Strategic-Recommendations", "next_task": "P02-S04-T02-Revenue-Strategy-Development"}, "P02-S04-T02-Revenue-Strategy-Development": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S04-T02", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T02-Revenue-Strategy-Development.md", "estimated_duration_minutes": 90, "previous_task": "P02-S04-T01-Business-Model-Design", "next_task": "P02-S04-T03-Go-to-Market-Strategy"}, "P02-S04-T03-Go-to-Market-Strategy": {"agent": "marketing-strategy-orchestrator", "phase": "phase_2", "task_id": "P02-S04-T03", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T03-Go-to-Market-Strategy.md", "estimated_duration_minutes": 105, "previous_task": "P02-S04-T02-Revenue-Strategy-Development", "next_task": "P02-S04-T04-Strategic-Partnership-Framework"}, "P02-S04-T04-Strategic-Partnership-Framework": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S04-T04", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T04-Strategic-Partnership-Framework.md", "estimated_duration_minutes": 90, "previous_task": "P02-S04-T03-Go-to-Market-Strategy", "next_task": "P02-S04-T05-Risk-Assessment-and-Mitigation"}, "P02-S04-T05-Risk-Assessment-and-Mitigation": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S04-T05", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T05-Risk-Assessment-and-Mitigation.md", "estimated_duration_minutes": 75, "previous_task": "P02-S04-T04-Strategic-Partnership-Framework", "next_task": "P02-S04-T06-Financial-Planning-and-Projections"}, "P02-S04-T06-Financial-Planning-and-Projections": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S04-T06", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T06-Financial-Planning-and-Projections.md", "estimated_duration_minutes": 120, "previous_task": "P02-S04-T05-Risk-Assessment-and-Mitigation", "next_task": "P02-S04-T07-Implementation-Strategy-and-Roadmap"}, "P02-S04-T07-Implementation-Strategy-and-Roadmap": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S04-T07", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T07-Implementation-Strategy-and-Roadmap.md", "estimated_duration_minutes": 105, "previous_task": "P02-S04-T06-Financial-Planning-and-Projections", "next_task": "P02-S04-T08-Strategy-Synthesis-and-Implementation-Planning"}, "P02-S04-T08-Strategy-Synthesis-and-Implementation-Planning": {"agent": "market-research-agent", "phase": "phase_2", "task_id": "P02-S04-T08", "file_path": "01_Machine/01_Workflow/Phase 2: Discovery & Strategy/04_Business_Strategy/P02-S04-T08-Strategy-Synthesis-and-Implementation-Planning.md", "estimated_duration_minutes": 90, "previous_task": "P02-S04-T07-Implementation-Strategy-and-Roadmap", "next_task": null}}, "tracking_functions": {"start_workflow": {"description": "Initialize workflow tracking for a new project", "updates": ["workflow_state.session", "workflow_state.current_step", "workflow_state.progress"], "sets_current_step": "P01-S01-T01-User-Profile-Development"}, "complete_step": {"description": "Mark current step as complete and move to next", "updates": ["workflow_state.previous_step", "workflow_state.current_step", "workflow_state.next_step", "workflow_state.progress"], "auto_progression": false}, "get_current_status": {"description": "Get current workflow position and progress", "returns": ["current_step", "previous_step", "next_step", "progress_percentage", "current_agent"]}, "update_progress": {"description": "Update progress within current step", "updates": ["workflow_state.current_task", "workflow_state.progress"]}}, "agent_instructions": {"start_command": "When user says 'Let's get started', call start_workflow() and begin with step P01-S01-T01-User-Profile-Development", "step_completion": "When step is complete, call complete_step() to move to next step with confirmation", "status_check": "Use get_current_status() to always know where you are in the workflow", "file_reference": "Always reference the current step's file_path for detailed instructions"}}